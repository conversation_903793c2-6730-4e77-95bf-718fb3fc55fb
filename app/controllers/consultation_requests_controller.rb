# frozen_string_literal: true

class ConsultationRequestsController < ApplicationController
  before_action :require_user
  before_action :set_consultation_request, only: [:show, :update, :destroy, :approve, :decline, :complete]
  before_action :require_student_access, only: [:create]
  before_action :require_faculty_access, only: [:approve, :decline, :complete, :faculty_dashboard]

  # GET /consultation_requests
  def index
    if @current_user.student_enrollments.active.exists?
      @requests = @current_user.student_consultation_requests.preload(:faculty, :faculty_time_slot)
                             .order(created_at: :desc)
    elsif @current_user.teacher_enrollments.active.exists?
      @requests = @current_user.faculty_consultation_requests.preload(:student, :faculty_time_slot)
                             .order(created_at: :desc)
    else
      @requests = ConsultationRequest.none
    end

    # Apply filters
    @requests = @requests.where(status: params[:status]) if params[:status].present?
    @requests = @requests.by_concern(params[:concern_type]) if params[:concern_type].present?

    respond_to do |format|
      format.json { render json: consultation_requests_json(@requests) }
      format.html { render_consultation_requests_page }
    end
  end

  # GET /consultation_requests/:id
  def show
    authorize_request_access!
    
    respond_to do |format|
      format.json { render json: consultation_request_json(@consultation_request) }
    end
  end

  # POST /consultation_requests
  def create
    @consultation_request = @current_user.student_consultation_requests.build(consultation_request_params)

    # Handle faculty_time_slot_id - it might be a composite ID like "slot_id-timestamp"
    faculty_time_slot_id = params[:consultation_request][:faculty_time_slot_id]
    if faculty_time_slot_id.present? && faculty_time_slot_id.to_s.include?('-')
      # Extract the actual faculty_time_slot_id from composite ID
      actual_slot_id = faculty_time_slot_id.to_s.split('-').first
      @consultation_request.faculty_time_slot_id = actual_slot_id
    end

    # Set the faculty from the faculty_time_slot
    if @consultation_request.faculty_time_slot
      @consultation_request.faculty = @consultation_request.faculty_time_slot.user
    end

    if @consultation_request.save
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request), status: :created }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @consultation_request.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /consultation_requests/:id
  def update
    authorize_request_access!
    
    if @consultation_request.update(consultation_request_update_params)
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @consultation_request.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /consultation_requests/:id
  def destroy
    authorize_request_access!
    
    if @consultation_request.pending?
      @consultation_request.cancel!
      respond_to do |format|
        format.json { head :no_content }
      end
    else
      respond_to do |format|
        format.json { render json: { error: 'Cannot cancel non-pending request' }, status: :unprocessable_entity }
      end
    end
  end

  # POST /consultation_requests/:id/approve
  def approve
    authorize_faculty_action!
    
    begin
      @consultation_request.approve!(@current_user, params[:comment])
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request) }
      end
    rescue ActiveRecord::RecordInvalid => e
      respond_to do |format|
        format.json { render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # POST /consultation_requests/:id/decline
  def decline
    authorize_faculty_action!
    
    comment = params[:comment]
    if comment.blank?
      respond_to do |format|
        format.json { render json: { errors: ['Comment is required when declining a request'] }, status: :unprocessable_entity }
      end
      return
    end

    begin
      @consultation_request.decline!(@current_user, comment)
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request) }
      end
    rescue ActiveRecord::RecordInvalid => e
      respond_to do |format|
        format.json { render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # POST /consultation_requests/:id/complete
  def complete
    authorize_faculty_action!
    
    begin
      @consultation_request.complete!(params[:completion_notes])
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request) }
      end
    rescue ActiveRecord::RecordInvalid => e
      respond_to do |format|
        format.json { render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # GET /consultation_requests/faculty_dashboard
  def faculty_dashboard
    @pending_requests = @current_user.faculty_consultation_requests.pending.preload(:student, :faculty_time_slot)
    @upcoming_consultations = @current_user.upcoming_consultations.limit(10)
    @statistics = @current_user.consultation_statistics

    respond_to do |format|
      format.json {
        render json: {
          pending_requests: consultation_requests_json(@pending_requests)[:requests],
          upcoming_consultations: consultation_requests_json(@upcoming_consultations)[:requests],
          statistics: @statistics
        }
      }
      format.html { render_faculty_dashboard_page }
    end
  end

  # GET /consultation_requests/faculty/:id/time_slots
  def faculty_time_slots
    begin
      faculty = User.find(params[:id])
      date_range = parse_date_range

      formatted_slots = build_faculty_time_slots(faculty, date_range)

      # Log debug information
      Rails.logger.info "Faculty Time Slots Request - Faculty: #{faculty.id}, Date Range: #{date_range.first} to #{date_range.last}"
      Rails.logger.info "Total slots found: #{formatted_slots.count}"

      render json: {
        time_slots: formatted_slots.sort_by { |slot| slot[:datetime] },
        debug_info: build_debug_info(faculty, date_range, formatted_slots)
      }
    rescue => e
      Rails.logger.error "Error in faculty_time_slots: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      render json: {
        error: e.message,
        time_slots: [],
        debug_info: { error: true, message: e.message }
      }, status: 500
    end
  end

  # GET /consultation_requests/student_form
  def student_form
    unless @current_user.student_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Student access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Student access required.'
          redirect_to root_path
        }
      end
      return
    end

    @available_faculty = @current_user.available_faculty_for_consultation.preload(:faculty_time_slots)

    respond_to do |format|
      format.json {
        render json: {
          student_info: student_info_json(@current_user),
          available_faculty: faculty_list_json(@available_faculty),
          concern_types: ConsultationRequest::NATURE_OF_CONCERNS
        }
      }
      format.html { render_student_form_page }
    end
  end

  private

  def parse_date_range
    start_date = params[:start_date].present? ? Date.parse(params[:start_date]) : Date.today
    end_date = params[:end_date].present? ? Date.parse(params[:end_date]) : (start_date + 7.days)
    start_date..end_date
  end

  def build_faculty_time_slots(faculty, date_range)
    formatted_slots = []

    # Get faculty's time slots (both recurring and specific date slots)
    recurring_slots = faculty.faculty_time_slots.available.recurring
    specific_date_slots = faculty.faculty_time_slots.available.specific_date
                                 .where(specific_date: date_range)

    # Process recurring slots
    formatted_slots.concat(process_recurring_slots(recurring_slots, date_range, faculty))

    # Process specific date slots
    formatted_slots.concat(process_specific_date_slots(specific_date_slots, faculty))

    formatted_slots
  end

  def process_recurring_slots(recurring_slots, date_range, faculty)
    slots = []
    return slots unless recurring_slots.any?

    date_range.each do |date|
      day_name = date.strftime('%A')
      recurring_slots.for_day(day_name).each do |slot|
        slot_time = build_slot_time(date, slot.start_time)
        slots << format_time_slot(slot, slot_time, faculty)
      end
    end

    slots
  end

  def process_specific_date_slots(specific_date_slots, faculty)
    specific_date_slots.map do |slot|
      slot_time = build_slot_time(slot.specific_date, slot.start_time)
      format_time_slot(slot, slot_time, faculty)
    end
  end

  def build_slot_time(date, time)
    Time.zone.local(date.year, date.month, date.day, time.hour, time.min)
  end

  def format_time_slot(slot, slot_time, faculty)
    is_booked = slot_booked?(slot, slot_time)

    {
      id: "#{slot.id}-#{slot_time.to_i}",
      datetime: slot_time.iso8601,
      formatted_time: slot_time.strftime("%I:%M %p"),
      is_available: slot.is_available && !is_booked,
      is_booked: is_booked,
      faculty_id: faculty.id,
      faculty_time_slot_id: slot.id,
      created_at: slot.created_at.iso8601,
      updated_at: slot.updated_at.iso8601
    }
  end

  def slot_booked?(slot, slot_time)
    slot.consultation_requests.where(
      status: ['approved', 'completed'],
      preferred_datetime: slot_time.beginning_of_hour..slot_time.end_of_hour
    ).exists?
  end

  def build_debug_info(faculty, date_range, formatted_slots)
    recurring_count = faculty.faculty_time_slots.available.recurring.count
    specific_count = faculty.faculty_time_slots.available.specific_date
                            .where(specific_date: date_range).count

    {
      faculty_id: faculty.id,
      start_date: date_range.first,
      end_date: date_range.last,
      recurring_slots_count: recurring_count,
      specific_date_slots_count: specific_count,
      total_slots_count: formatted_slots.count
    }
  end

  def require_student_access
    unless @current_user.student_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Student access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Student access required.'
          redirect_to root_path
        }
      end
    end
  end

  def require_faculty_access
    unless @current_user.teacher_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Faculty access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Faculty access required.'
          redirect_to root_path
        }
      end
    end
  end

  def set_consultation_request
    @consultation_request = ConsultationRequest.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { error: 'Consultation request not found' }, status: :not_found }
      format.html { 
        flash[:error] = 'Consultation request not found'
        redirect_to consultation_requests_path 
      }
    end
  end

  def authorize_request_access!
    unless @consultation_request.student == @current_user || @consultation_request.faculty == @current_user
      respond_to do |format|
        format.json { render json: { error: 'Access denied' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied'
          redirect_to consultation_requests_path
        }
      end
    end
  end

  def authorize_faculty_action!
    unless @consultation_request.faculty == @current_user
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Only the assigned faculty can perform this action.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied'
          redirect_to consultation_requests_path
        }
      end
    end
  end

  def consultation_request_params
    params.require(:consultation_request).permit(
      :faculty_time_slot_id, :preferred_datetime, :description, :nature_of_concern, :custom_concern,
      :college_campus_institute, :department_program, :semester, :academic_year,
      :place_of_consultation, :intervention_given, :referral_made,
      :students_adviser_agreement, :prepared_by_name, :prepared_by_designation,
      :noted_by_program_chair, :noted_by_college_dean, :conformance_signature
    )
  end

  def consultation_request_update_params
    params.require(:consultation_request).permit(
      :description, :nature_of_concern, :custom_concern, :college_campus_institute,
      :department_program, :semester, :academic_year, :place_of_consultation,
      :intervention_given, :referral_made, :students_adviser_agreement,
      :prepared_by_name, :prepared_by_designation, :noted_by_program_chair,
      :noted_by_college_dean, :conformance_signature
    )
  end

  def consultation_request_json(request)
    {
      id: request.id,
      student_name: request.student_name,
      student_id: request.student_id,
      faculty_name: request.faculty.name,
      preferred_datetime: request.preferred_datetime.iso8601,
      formatted_datetime: request.formatted_preferred_datetime,
      description: request.description,
      nature_of_concern: request.nature_of_concern,
      custom_concern: request.custom_concern,
      concern_type_display: request.concern_type_display,
      status: request.status,
      status_display: request.status_display,
      faculty_comment: request.faculty_comment,
      created_at: request.created_at.iso8601,
      updated_at: request.updated_at.iso8601,
      can_be_approved: request.can_be_approved?,
      can_be_declined: request.can_be_declined?,
      can_be_completed: request.can_be_completed?,
      college_campus_institute: request.college_campus_institute,
      department_program: request.department_program,
      semester: request.semester,
      academic_year: request.academic_year,
      place_of_consultation: request.place_of_consultation,
      intervention_given: request.intervention_given,
      referral_made: request.referral_made,
      students_adviser_agreement: request.students_adviser_agreement,
      prepared_by_name: request.prepared_by_name,
      prepared_by_designation: request.prepared_by_designation,
      noted_by_program_chair: request.noted_by_program_chair,
      noted_by_college_dean: request.noted_by_college_dean,
      conformance_signature: request.conformance_signature,
      scf_number: request.scf_number
    }
  end

  def consultation_requests_json(requests)
    {
      requests: requests.map { |request| consultation_request_json(request) },
      total_count: requests.count
    }
  end

  def faculty_list_json(faculty_users)
    faculty_users.map do |faculty|
      {
        id: faculty.id,
        name: faculty.name,
        department: faculty.department,
        available_slots_count: faculty.faculty_time_slots.available.count
      }
    end
  end

  def render_consultation_requests_page
    @page_title = @current_user.is_student? ? 'My Consultation Requests' : 'Consultation Requests'
    js_env({
      CONSULTATION_REQUESTS: {
        current_user_id: @current_user.id,
        user_role: @current_user.student_enrollments.active.exists? ? 'student' : 'faculty',
        requests: consultation_requests_json(@requests)[:requests],
        concern_types: ConsultationRequest::NATURE_OF_CONCERNS,
        statuses: ConsultationRequest::STATUSES
      }
    })
    
    js_bundle :consultation_requests
    css_bundle :consultation_system
    render html: "".html_safe, layout: true
  end

  def render_faculty_dashboard_page
    @page_title = 'Faculty Consultation Dashboard'
    js_env({
      FACULTY_DASHBOARD: {
        current_user_id: @current_user.id,
        pending_requests: consultation_requests_json(@pending_requests)[:requests],
        upcoming_consultations: consultation_requests_json(@upcoming_consultations)[:requests],
        statistics: @statistics
      }
    })
    
    js_bundle :faculty_consultation_dashboard
    css_bundle :consultation_system
    render html: "".html_safe, layout: true
  end

  def render_student_form_page
    @page_title = 'Request Consultation'
    js_env({
      STUDENT_CONSULTATION_FORM: {
        current_user_id: @current_user.id,
        student_info: student_info_json(@current_user),
        available_faculty: faculty_list_json(@available_faculty),
        concern_types: ConsultationRequest::NATURE_OF_CONCERNS
      }
    })

    js_bundle :student_consultation_form
    css_bundle :consultation_system
    render :student_form
  end

  def student_info_json(user)
    # Get the student's active enrollment to extract course and section information
    active_enrollment = user.student_enrollments.active.preload(:course, :course_section).first

    # Try to get student record if it exists
    student_record = user.student if user.respond_to?(:student)

    student_info = {
      name: user.name,
      student_id: user.pseudonyms.first&.unique_id || "STU#{user.id}",
      department: user.department,
      semester: student_record&.semester,
      academic_year: student_record&.standing_year,
      college_campus_institute: nil,
      section: nil
    }

    if active_enrollment
      course = active_enrollment.course
      section = active_enrollment.course_section

      # Extract course/program information
      student_info[:department_program] = course.name if course
      student_info[:section] = section.name if section
      student_info[:college_campus_institute] = course.account.name if course&.account
    end

    student_info
  end
end
